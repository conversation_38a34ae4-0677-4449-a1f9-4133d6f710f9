import os
import json
import uuid
import base64
import requests
from datetime import datetime
from common.logger import logger
from common.decorator import api_log_io
from urllib.parse import urlparse
from fastapi import APIRouter, UploadFile, File, HTTPException, Form, Request, Header
from starlette.status import HTTP_400_BAD_REQUEST
from typing import Optional
from etc.config import FILES_DIR
from src.ocr.biz.ocr_engine import OCREngine
from src.common.consts import header_keys
from src.common.response_model import response
from src.ocr.dto.orc_file import OcrExtractResponse
from src.ocr.dto.orc_tcc import TCOCRDataDTO
from src.ocr.biz.ocr_tencent import TencentOcrAPI, TencentOcrRequestError, extract_ocr_key_values

router = APIRouter()

ALLOWED_EXTENSIONS = {'.pdf', '.png', '.jpeg', '.jpg', ".docx", ".xlsx", ".xls", ".pptx"}


def get_extension(filename: str) -> str:
    return os.path.splitext(filename)[-1].lower()


def is_valid_url(value: str) -> bool:
    return value.startswith("http://") or value.startswith("https://")


@router.post("/structure", response_model=OcrExtractResponse)
@api_log_io()
def ocr_file_path_extract_api(
        request: Request,
        path: str = Form(..., description="文件路径或远程文件地址"),
        ocrModel: str = Form(...,
                             description="OCR调用模型, [paddle(飞桨)、SmartStructuralPro(文档抽取（多模态版））、"
                                         "SmartStructuralOCRV2（文档抽取（基础版））、RecognizeTableAccurateOCR （表格识别V3）]"),
        page_scope: str = Form(None, description="页码范围"),
        page_rich_scope: str = Form(None,
                                    description="用大模型加持优化的页码范围；支持格式：1,2,3 或 1-3 或混合的 1,2,5-7,10-12"),
        scene_1: Optional[str] = Header(None, description="一级场景标签"),
        scene_2: Optional[str] = Header(None, description="二级场景标签"),
        scene_3: Optional[str] = Header(None, description="三级场景标签"),
        scene_4: Optional[str] = Header(None, description="四级场景标签"),
        usr: Optional[str] = Header(None, description="用户标识")
):
    """
    OCR 文件内容提取（同步版本）
    """
    start_time = datetime.now()
    llm_headers = {
        key: request.headers[key]
        for key in header_keys
        if key in request.headers
    }
    llm_headers["model"] = ocrModel

    is_remote = path.startswith("http://") or path.startswith("https://")

    if is_remote:
        os.makedirs(FILES_DIR, exist_ok=True)
        parsed_url = urlparse(path)
        original_filename = os.path.basename(parsed_url.path)
        ext = os.path.splitext(original_filename)[-1].lower()

        if ext not in ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"远程文件格式不支持：{ext}")

        temp_filename = f"{uuid.uuid4().hex}{ext}"
        local_path = os.path.join(FILES_DIR, temp_filename)

        try:
            resp = requests.get(path)
            if resp.status_code != 200:
                raise HTTPException(status_code=400, detail="下载远程文件失败")
            with open(local_path, "wb") as f:
                f.write(resp.content)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"下载远程文件异常：{str(e)}")

        file_name = original_filename

    else:
        local_path = path
        if not os.path.isfile(local_path):
            raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail="提供的文件路径不存在或不是文件")

        ext = os.path.splitext(local_path)[-1].lower()
        if ext not in ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"不支持的文件格式：{ext}")

        file_name = os.path.basename(local_path)

    try:
        ocr_biz = OCREngine(local_path, ext, llm_headers)
        if ocrModel == "paddle":
            result = ocr_biz.ocr_extract(page_scope, page_rich_scope)
        elif ocrModel == "SmartStructuralPro":  # 文档抽取（多模态版）
            result = ocr_biz.tc_ocr_extract_smart_structural_pro(page_scope, page_rich_scope)
        elif ocrModel == "SmartStructuralOCRV2":  # 文档抽取（基础版）
            result = ocr_biz.tc_ocr_extract_structural_v2(page_scope, page_rich_scope)
        elif ocrModel == "RecognizeTableAccurateOCR": # 表格识别V3
            result = ocr_biz.tc_ocr_extract_table_v3(page_scope)
        else:
            raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"OCR调用模型不可为空")
        end_time = datetime.now()
        cost = int((end_time.timestamp() - start_time.timestamp()) * 1000)
        print(f"{end_time}\t2md\t{cost}\t\"{file_name}\"")
        data = {
            'pages': result,
            'cost': cost
        }
        return response(code="0", msg="OCR 文件内容提取", data=data)

    except Exception as e:
        logger.error(f"OCR提取失败: {e}")
        return response(code="-1", msg=f"An error occurred: {e}")


@router.post("")
@api_log_io()
def ocr_upload_file_api(
        file: UploadFile = File(..., description="文件流")):
    """
    OCR上传文件
    :param file:
    :return:
    """
    ocr_biz = OCREngine()
    text = ocr_biz.ocr_file(file)
    return response(code="0", msg="OCR 文件内容提取", data={"text": text})
